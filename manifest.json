{"name": "EAM - Elashrafy AI Model", "short_name": "EAM", "description": "نموذج الذكاء الاصطناعي الأشرافي للمحادثات الذكية", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#2563eb", "orientation": "portrait-primary", "lang": "ar", "dir": "rtl", "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 192 192'><rect width='192' height='192' fill='%232563eb' rx='24'/><text x='96' y='130' font-size='100' text-anchor='middle' fill='white'>🤖</text></svg>", "sizes": "192x192", "type": "image/svg+xml", "purpose": "any maskable"}, {"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'><rect width='512' height='512' fill='%232563eb' rx='64'/><text x='256' y='350' font-size='280' text-anchor='middle' fill='white'>🤖</text></svg>", "sizes": "512x512", "type": "image/svg+xml", "purpose": "any maskable"}], "categories": ["productivity", "utilities", "education"], "screenshots": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1280 720'><rect width='1280' height='720' fill='%23f8f9fa'/><text x='640' y='360' font-size='48' text-anchor='middle' fill='%232563eb'>EAM - Elashrafy AI Model</text></svg>", "sizes": "1280x720", "type": "image/svg+xml", "form_factor": "wide"}]}